ENVIRONMENT:
  MAX_EPISODE_STEPS: 500
SIMULATOR:
  ACTION_SPACE_CONFIG: v0
  AGENT_0:
    SENSORS: [RGB_SENSOR, DEPTH_SENSOR]
  FORWARD_STEP_SIZE: 0.25
  TURN_ANGLE: 15
  HABITAT_SIM_V0:
    GPU_DEVICE_ID: 0
    ALLOW_SLIDING: True
  RGB_SENSOR:
    WIDTH: 224
    HEIGHT: 224
    HFOV: 90
    TYPE: HabitatSimRGBSensor
  DEPTH_SENSOR:
    WIDTH: 256
    HEIGHT: 256
TASK:
  TYPE: VLN-v0
  SUCCESS_DISTANCE: 3.0
  SENSORS: [
    INSTRUCTION_SENSOR,
    SHORTEST_PATH_SENSOR,
    VLN_ORACLE_PROGRESS_SENSOR
  ]
  INSTRUCTION_SENSOR_UUID: instruction
  POSSIBLE_ACTIONS: [STOP, MOVE_FORWARD, TURN_LEFT, TURN_RIGHT]
  MEASUREMENTS: [
    DISTANCE_TO_GOAL,
    PATH_LENGTH,
    ORACLE_SUCCESS,
  ]
  SUCCESS:
    SUCCESS_DISTANCE: 3.0
  SPL:
    SUCCESS_DISTANCE: 3.0
NDTW:
    GT_PATH: datasets/R2R/datasets/R2R_VLNCE_v1-3_preprocessed/{split}/{split}_gt.json.gz
DATASET:
  TYPE: VLN-CE-v1
  SPLIT: val_unseen
  DATA_PATH: datasets/R2R/datasets/R2R_VLNCE_v1-3_preprocessed/{split}/{split}.json.gz
  SCENES_DIR: datasets/R2R/scene_datasets/


name: rlds_env
channels:
  - conda-forge
dependencies:
  - _libgcc_mutex=0.1=conda_forge
  - _openmp_mutex=4.5=2_gnu
  - ca-certificates=2023.7.22=hbcca054_0
  - ld_impl_linux-64=2.40=h41732ed_0
  - libffi=3.3=h58526e2_2
  - libgcc-ng=13.1.0=he5830b7_0
  - libgomp=13.1.0=he5830b7_0
  - libsqlite=3.42.0=h2797004_0
  - libstdcxx-ng=13.1.0=hfd8a6a1_0
  - libzlib=1.2.13=hd590300_5
  - ncurses=6.4=hcb278e6_0
  - openssl=1.1.1u=hd590300_0
  - pip=23.2.1=pyhd8ed1ab_0
  - python=3.9.0=hffdb5ce_5_cpython
  - readline=8.2=h8228510_1
  - setuptools=68.0.0=pyhd8ed1ab_0
  - sqlite=3.42.0=h2c6b66d_0
  - tk=8.6.12=h27826a3_0
  - tzdata=2023c=h71feb2d_0
  - wheel=0.41.0=pyhd8ed1ab_0
  - xz=5.2.6=h166bdaf_0
  - zlib=1.2.13=hd590300_5
  - pip:
      - absl-py==1.4.0
      - anyio==3.7.1
      - apache-beam==2.49.0
      - appdirs==1.4.4
      - array-record==0.4.0
      - astunparse==1.6.3
      - cachetools==5.3.1
      - certifi==2023.7.22
      - charset-normalizer==3.2.0
      - click==8.1.6
      - cloudpickle==2.2.1
      - contourpy==1.1.0
      - crcmod==1.7
      - cycler==0.11.0
      - dill==*******
      - dm-tree==0.1.8
      - dnspython==2.4.0
      - docker-pycreds==0.4.0
      - docopt==0.6.2
      - etils==1.3.0
      - exceptiongroup==1.1.2
      - fastavro==1.8.2
      - fasteners==0.18
      - flatbuffers==23.5.26
      - fonttools==4.41.1
      - gast==0.4.0
      - gitdb==4.0.10
      - gitpython==3.1.32
      - google-auth==2.22.0
      - google-auth-oauthlib==1.0.0
      - google-pasta==0.2.0
      - googleapis-common-protos==1.59.1
      - grpcio==1.56.2
      - h11==0.14.0
      - h5py==3.9.0
      - hdfs==2.7.0
      - httpcore==0.17.3
      - httplib2==0.22.0
      - idna==3.4
      - importlib-metadata==6.8.0
      - importlib-resources==6.0.0
      - keras==2.13.1
      - kiwisolver==1.4.4
      - libclang==16.0.6
      - markdown==3.4.3
      - markupsafe==2.1.3
      - matplotlib==3.7.2
      - numpy==1.24.3
      - oauthlib==3.2.2
      - objsize==0.6.1
      - opt-einsum==3.3.0
      - orjson==3.9.2
      - packaging==23.1
      - pathtools==0.1.2
      - pillow==10.0.0
      - plotly==5.15.0
      - promise==2.3
      - proto-plus==1.22.3
      - protobuf==4.23.4
      - psutil==5.9.5
      - pyarrow==11.0.0
      - pyasn1==0.5.0
      - pyasn1-modules==0.3.0
      - pydot==1.4.2
      - pymongo==4.4.1
      - pyparsing==3.0.9
      - python-dateutil==2.8.2
      - pytz==2023.3
      - pyyaml==6.0.1
      - regex==2023.6.3
      - requests==2.31.0
      - requests-oauthlib==1.3.1
      - rsa==4.9
      - sentry-sdk==1.28.1
      - setproctitle==1.3.2
      - six==1.16.0
      - smmap==5.0.0
      - sniffio==1.3.0
      - tenacity==8.2.2
      - tensorboard==2.13.0
      - tensorboard-data-server==0.7.1
      - tensorflow==2.13.0
      - tensorflow-datasets==4.9.2
      - tensorflow-estimator==2.13.0
      - tensorflow-hub==0.14.0
      - tensorflow-io-gcs-filesystem==0.32.0
      - tensorflow-metadata==1.13.1
      - termcolor==2.3.0
      - toml==0.10.2
      - tqdm==4.65.0
      - typing-extensions==4.5.0
      - urllib3==1.26.16
      - wandb==0.15.6
      - werkzeug==2.3.6
      - wrapt==1.15.0
      - zipp==3.16.2
      - zstandard==0.21.0
prefix: /scr/kpertsch/miniconda3/envs/rlds_env

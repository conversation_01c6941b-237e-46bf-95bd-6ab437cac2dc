name: rlds_env
channels:
  - defaults
dependencies:
  - _tflow_select=2.2.0=eigen
  - abseil-cpp=20211102.0=he9d5cce_0
  - aiosignal=1.2.0=pyhd3eb1b0_0
  - appdirs=1.4.4=pyhd3eb1b0_0
  - astunparse=1.6.3=py_0
  - blas=1.0=mkl
  - bzip2=1.0.8=h1de35cc_0
  - c-ares=1.19.0=h6c40b1e_0
  - ca-certificates=2023.05.30=hecd8cb5_0
  - cachetools=4.2.2=pyhd3eb1b0_0
  - charset-normalizer=2.0.4=pyhd3eb1b0_0
  - flatbuffers=2.0.0=h23ab428_0
  - gast=0.4.0=pyhd3eb1b0_0
  - giflib=5.2.1=h6c40b1e_3
  - google-auth=2.6.0=pyhd3eb1b0_0
  - google-pasta=0.2.0=pyhd3eb1b0_0
  - grpc-cpp=1.48.2=h3afe56f_0
  - hdf5=1.10.6=h10fe05b_1
  - icu=68.1=h23ab428_0
  - intel-openmp=2023.1.0=ha357a0b_43547
  - jpeg=9e=h6c40b1e_1
  - keras-preprocessing=1.1.2=pyhd3eb1b0_0
  - krb5=1.20.1=hdba6334_1
  - libcurl=8.1.1=ha585b31_1
  - libcxx=14.0.6=h9765a3e_0
  - libedit=3.1.20221030=h6c40b1e_0
  - libev=4.33=h9ed2024_1
  - libffi=3.4.4=hecd8cb5_0
  - libgfortran=5.0.0=11_3_0_hecd8cb5_28
  - libgfortran5=11.3.0=h9dfd629_28
  - libnghttp2=1.52.0=h1c88b7d_1
  - libpng=1.6.39=h6c40b1e_0
  - libprotobuf=3.20.3=hfff2838_0
  - libssh2=1.10.0=hdb2fb19_2
  - llvm-openmp=14.0.6=h0dcd299_0
  - mkl=2023.1.0=h59209a4_43558
  - mkl_fft=1.3.6=py311hdb55bb0_1
  - mkl_random=1.2.2=py311hdb55bb0_1
  - ncurses=6.4=hcec6c5f_0
  - numpy-base=1.23.5=py311h53bf9ac_1
  - openssl=1.1.1u=hca72f7f_0
  - opt_einsum=3.3.0=pyhd3eb1b0_1
  - pooch=1.4.0=pyhd3eb1b0_0
  - pyasn1=0.4.8=pyhd3eb1b0_0
  - pyasn1-modules=0.2.8=py_0
  - pycparser=2.21=pyhd3eb1b0_0
  - python=3.11.4=h1fd4e5f_0
  - python-flatbuffers=2.0=pyhd3eb1b0_0
  - re2=2022.04.01=he9d5cce_0
  - readline=8.2=hca72f7f_0
  - requests-oauthlib=1.3.0=py_0
  - rsa=4.7.2=pyhd3eb1b0_1
  - six=1.16.0=pyhd3eb1b0_1
  - snappy=1.1.9=he9d5cce_0
  - sqlite=3.41.2=h6c40b1e_0
  - tbb=2021.8.0=ha357a0b_0
  - tensorboard-plugin-wit=1.6.0=py_0
  - tensorflow-base=2.12.0=eigen_py311hbf87084_0
  - tk=8.6.12=h5d9f67b_0
  - typing_extensions=4.6.3=py311hecd8cb5_0
  - tzdata=2023c=h04d1e81_0
  - wheel=0.35.1=pyhd3eb1b0_0
  - xz=5.4.2=h6c40b1e_0
  - zlib=1.2.13=h4dc903c_0
  - pip:
    - absl-py==1.4.0
    - aiohttp==3.8.3
    - apache-beam==2.48.0
    - array-record==0.4.0
    - async-timeout==4.0.2
    - attrs==22.1.0
    - blinker==1.4
    - brotlipy==0.7.0
    - certifi==2023.5.7
    - cffi==1.15.1
    - click==8.0.4
    - cloudpickle==2.2.1
    - contourpy==1.1.0
    - crcmod==1.7
    - cryptography==39.0.1
    - cycler==0.11.0
    - dill==*******
    - dm-tree==0.1.8
    - dnspython==2.3.0
    - docker-pycreds==0.4.0
    - docopt==0.6.2
    - etils==1.3.0
    - fastavro==1.8.0
    - fasteners==0.18
    - fonttools==4.41.0
    - frozenlist==1.3.3
    - gitdb==4.0.10
    - gitpython==3.1.32
    - google-auth-oauthlib==0.5.2
    - googleapis-common-protos==1.59.1
    - grpcio==1.48.2
    - h5py==3.7.0
    - hdfs==2.7.0
    - httplib2==0.22.0
    - idna==3.4
    - importlib-resources==6.0.0
    - keras==2.12.0
    - kiwisolver==1.4.4
    - markdown==3.4.1
    - markupsafe==2.1.1
    - matplotlib==3.7.2
    - mkl-fft==1.3.6
    - mkl-random==1.2.2
    - mkl-service==2.4.0
    - multidict==6.0.2
    - numpy==1.23.5
    - oauthlib==3.2.2
    - objsize==0.6.1
    - orjson==3.9.2
    - packaging==23.0
    - pathtools==0.1.2
    - pillow==10.0.0
    - pip==23.1.2
    - plotly==5.15.0
    - promise==2.3
    - proto-plus==1.22.3
    - protobuf==3.20.3
    - psutil==5.9.5
    - pyarrow==11.0.0
    - pydot==1.4.2
    - pyjwt==2.4.0
    - pymongo==4.4.1
    - pyopenssl==23.0.0
    - pyparsing==3.0.9
    - pysocks==1.7.1
    - python-dateutil==2.8.2
    - pytz==2023.3
    - pyyaml==6.0
    - regex==2023.6.3
    - requests==2.29.0
    - scipy==1.10.1
    - sentry-sdk==1.28.1
    - setproctitle==1.3.2
    - setuptools==67.8.0
    - smmap==5.0.0
    - tenacity==8.2.2
    - tensorboard==2.12.1
    - tensorboard-data-server==0.7.0
    - tensorflow==2.12.0
    - tensorflow-datasets==4.9.2
    - tensorflow-estimator==2.12.0
    - tensorflow-hub==0.14.0
    - tensorflow-metadata==1.13.1
    - termcolor==2.1.0
    - toml==0.10.2
    - tqdm==4.65.0
    - typing-extensions==4.6.3
    - urllib3==1.26.16
    - wandb==0.15.5
    - werkzeug==2.2.3
    - wrapt==1.14.1
    - yarl==1.8.1
    - zipp==3.16.1
    - zstandard==0.21.0
prefix: /Users/<USER>/miniconda3/envs/rlds_env
